import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Clock, Save } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import { useAuth } from '../../../hooks/useAuth';
import { GeminiNotificationSettings, NotificationPriority } from '../../../types/notifications';
import { THEME_COLORS, THEME_CLASSES } from '../../../styles/colors';

/**
 * Composant pour configurer les paramètres Gemini
 */
export const GeminiSettings: React.FC = () => {
  const { user } = useAuth();
  const [settings, setSettings] = useState<GeminiNotificationSettings>({
    enableAutoRecommendations: true,
    checkFrequencyHours: 24,
    enableSafetyAlerts: true,
    minimumPriority: 'medium'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaved, setIsSaved] = useState(false);

  // Charger les paramètres existants
  useEffect(() => {
    // TODO: Charger depuis Firestore
    // loadGeminiSettings(user?.uid);
  }, [user]);

  const handleSave = async () => {
    if (!user) return;
    
    setIsLoading(true);
    try {
      // TODO: Sauvegarder dans Firestore
      // await saveGeminiSettings(user.uid, settings);
      setIsSaved(true);
      setTimeout(() => setIsSaved(false), 3000);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de la sauvegarde:', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const priorityOptions: { value: NotificationPriority; label: string; color: string }[] = [
    { value: 'low', label: 'Faible', color: 'bg-green-100 text-green-800' },
    { value: 'medium', label: 'Moyenne', color: 'bg-yellow-100 text-yellow-800' },
    { value: 'high', label: 'Élevée', color: 'bg-orange-100 text-orange-800' },
    { value: 'urgent', label: 'Urgente', color: 'bg-red-100 text-red-800' }
  ];

  const frequencyOptions = [
    { value: 6, label: '6 heures' },
    { value: 12, label: '12 heures' },
    { value: 24, label: '24 heures' },
    { value: 48, label: '48 heures' },
    { value: 72, label: '3 jours' }
  ];

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* En-tête */}
      <div className="flex items-center gap-3">
        <Brain className="h-8 w-8 text-[#d385f5]" />
        <div>
          <h1 className="text-2xl font-bold text-white">Paramètres Gemini IA</h1>
          <p className="text-[#E0E0E0]">Configurez l'intelligence artificielle pour vos plantes</p>
        </div>
      </div>

      {/* Statut de l'IA */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-[#d385f5]" />
            Statut de l'IA
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="font-medium">Gemini 2.5 Flash</span>
              <Badge variant="secondary">Gratuit</Badge>
            </div>
            <div className="text-sm text-[#E0E0E0]">
              Dernière analyse: Il y a 2 heures
            </div>
          </div>
          <div className="mt-4 p-3 bg-[#1c1a31] border border-[#d385f5] rounded-lg">
            <p className="text-sm text-[#E0E0E0]">
              🤖 L'IA Gemini analyse vos plantes en continu et génère des recommandations personnalisées
              basées sur l'historique, la saison et le type de plante.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Paramètres des recommandations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Recommandations automatiques
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Activation des recommandations */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Recommandations intelligentes</h3>
              <p className="text-sm text-gray-600">
                Génère automatiquement des conseils personnalisés
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.enableAutoRecommendations}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  enableAutoRecommendations: e.target.checked
                }))}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>

          {/* Fréquence de vérification */}
          <div>
            <h3 className="font-medium mb-2">Fréquence de vérification</h3>
            <div className="grid grid-cols-5 gap-2">
              {frequencyOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => setSettings(prev => ({
                    ...prev,
                    checkFrequencyHours: option.value
                  }))}
                  className={`p-2 text-sm rounded-lg border transition-colors ${
                    settings.checkFrequencyHours === option.value
                      ? 'border-[#d385f5] bg-[#d385f5] text-white'
                      : 'border-gray-600 hover:border-[#d385f5] text-[#E0E0E0] hover:text-white'
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>

          {/* Priorité minimum */}
          <div>
            <h3 className="font-medium mb-2">Priorité minimum des notifications</h3>
            <div className="grid grid-cols-4 gap-2">
              {priorityOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => setSettings(prev => ({
                    ...prev,
                    minimumPriority: option.value
                  }))}
                  className={`p-2 text-sm rounded-lg border transition-colors ${
                    settings.minimumPriority === option.value
                      ? 'border-[#d385f5] bg-[#d385f5]'
                      : 'border-gray-600 hover:border-[#d385f5]'
                  }`}
                >
                  <Badge className={option.color}>
                    {option.label}
                  </Badge>
                </button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Alertes de sécurité */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-red-600" />
            Alertes de sécurité
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Alertes critiques</h3>
              <p className="text-sm text-gray-600">
                Notifications immédiates pour les problèmes graves (pourriture, flétrissement)
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.enableSafetyAlerts}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  enableSafetyAlerts: e.target.checked
                }))}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
            </label>
          </div>
        </CardContent>
      </Card>

      {/* Aperçu des fonctionnalités */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5 text-blue-600" />
            Fonctionnalités IA activées
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3">
            <div className="flex items-center gap-3 p-3 bg-[#1c1a31] border border-[#d385f5] rounded-lg">
              <div className="w-2 h-2 bg-[#d385f5] rounded-full"></div>
              <span className="text-sm text-[#E0E0E0]">Calculs d'échéances intelligents selon la saison</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-[#1c1a31] border border-[#d385f5] rounded-lg">
              <div className="w-2 h-2 bg-[#d385f5] rounded-full"></div>
              <span className="text-sm text-[#E0E0E0]">Adaptation selon le type de plante</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-[#1c1a31] border border-[#d385f5] rounded-lg">
              <div className="w-2 h-2 bg-[#d385f5] rounded-full"></div>
              <span className="text-sm text-[#E0E0E0]">Apprentissage basé sur l'historique</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-[#1c1a31] border border-[#d385f5] rounded-lg">
              <div className="w-2 h-2 bg-[#d385f5] rounded-full"></div>
              <span className="text-sm text-[#E0E0E0]">Détection des patterns de maladies récurrentes</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-[#1c1a31] border border-[#d385f5] rounded-lg">
              <div className="w-2 h-2 bg-[#d385f5] rounded-full"></div>
              <span className="text-sm text-[#E0E0E0]">Recommandations préventives personnalisées</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bouton de sauvegarde */}
      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          disabled={isLoading}
          className={`flex items-center gap-2 ${
            isSaved ? 'bg-[#d385f5] hover:bg-[#c070e0]' : 'bg-[#d385f5] hover:bg-[#c070e0]'
          } text-white`}
        >
          {isLoading ? (
            <Clock className="h-4 w-4 animate-spin" />
          ) : isSaved ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-white rounded-full flex items-center justify-center">
                <div className="w-2 h-2 bg-[#d385f5] rounded-full"></div>
              </div>
              Sauvegardé
            </div>
          ) : (
            <>
              <Save className="h-4 w-4" />
              Sauvegarder
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default GeminiSettings;
