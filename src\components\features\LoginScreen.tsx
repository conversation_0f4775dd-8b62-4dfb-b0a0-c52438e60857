
import React from 'react';
import { motion } from 'framer-motion';
import { signInWithGoogle } from '@/services/api';
import { Button } from '@/components/common/Button';
import { LeafIcon } from '@/components/common/icons';

const LoginScreen: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-[#100f1c] p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md text-center"
      >
        <div className="inline-block p-4 bg-gradient-to-r from-[#d385f5] to-[#a364f7] rounded-full mb-6">
          <LeafIcon className="w-16 h-16 text-white" />
        </div>
        <h1 className="text-5xl font-bold text-white mb-2">FloraSynth</h1>
        <p className="text-lg text-[#E0E0E0] mb-8">Votre Assistant IA Personnel pour le Soin des Plantes</p>
        <Button onClick={signInWithGoogle} className="w-full max-w-xs mx-auto">
          Se connecter avec Google
        </Button>
      </motion.div>
    </div>
  );
};

export default LoginScreen;
