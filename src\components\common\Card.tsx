
import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';

interface CardProps {
  children: ReactNode;
  className?: string;
  onClick?: () => void;
}

export const Card: React.FC<CardProps> = ({ children, className = '', onClick }) => {
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };
  
  return (
    <motion.div
      variants={cardVariants}
      className={`bg-[#1c1a31] p-6 rounded-2xl shadow-lg ${onClick ? 'cursor-pointer transition-all duration-300 hover:shadow-2xl hover:shadow-[#a364f7]/20' : ''} ${className}`}
      onClick={onClick}
    >
      {children}
    </motion.div>
  );
};
