import React, { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon, BeakerIcon } from '@heroicons/react/24/outline';
import { FERTILIZERS_DATABASE, Fertilizer } from '../data/fertilizers';
import { DEFICIENCY_GUIDES, DeficiencyGuide } from '../data/plant-care-guide';

interface FertilizerGuideProps {
  isOpen: boolean;
  onToggle: () => void;
}

export const FertilizerGuide: React.FC<FertilizerGuideProps> = ({ isOpen, onToggle }) => {
  const [activeTab, setActiveTab] = useState<'fertilizers' | 'deficiencies'>('fertilizers');
  const [selectedFertilizer, setSelectedFertilizer] = useState<Fertilizer | null>(null);
  const [selectedDeficiency, setSelectedDeficiency] = useState<DeficiencyGuide | null>(null);

  if (!isOpen) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-4 right-4 bg-[#d385f5] hover:bg-[#c070e0] text-white p-3 rounded-full shadow-lg transition-all duration-200 z-50"
      >
        <BeakerIcon className="w-6 h-6" />
      </button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-[#1c1a31] rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-[#3D3B5E]">
          <h2 className="text-2xl font-bold text-white flex items-center gap-2">
            <BeakerIcon className="w-8 h-8 text-[#d385f5]" />
            Guide des Engrais et Carences
          </h2>
          <button
            onClick={onToggle}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <ChevronUpIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-[#3D3B5E]">
          <button
            onClick={() => setActiveTab('fertilizers')}
            className={`flex-1 py-3 px-6 text-center transition-colors ${
              activeTab === 'fertilizers'
                ? 'bg-[#d385f5] text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Engrais Disponibles
          </button>
          <button
            onClick={() => setActiveTab('deficiencies')}
            className={`flex-1 py-3 px-6 text-center transition-colors ${
              activeTab === 'deficiencies'
                ? 'bg-[#d385f5] text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Guide des Carences
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {activeTab === 'fertilizers' ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {FERTILIZERS_DATABASE.map((fertilizer) => (
                  <div
                    key={fertilizer.id}
                    className="bg-[#100f1c] p-4 rounded-lg border border-[#3D3B5E] cursor-pointer hover:border-[#d385f5] transition-colors"
                    onClick={() => setSelectedFertilizer(
                      selectedFertilizer?.id === fertilizer.id ? null : fertilizer
                    )}
                  >
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-[#d385f5]">{fertilizer.nom}</h3>
                      {selectedFertilizer?.id === fertilizer.id ? (
                        <ChevronUpIcon className="w-5 h-5 text-gray-400" />
                      ) : (
                        <ChevronDownIcon className="w-5 h-5 text-gray-400" />
                      )}
                    </div>
                    <p className="text-sm text-gray-400 mt-1">{fertilizer.composition_npk}</p>
                    
                    {selectedFertilizer?.id === fertilizer.id && (
                      <div className="mt-4 space-y-3 text-sm">
                        <div>
                          <h4 className="font-medium text-white mb-1">Description :</h4>
                          <p className="text-gray-300">{fertilizer.description_action}</p>
                        </div>
                        
                        <div>
                          <h4 className="font-medium text-white mb-1">Plantes cibles :</h4>
                          <p className="text-gray-300">{fertilizer.plantes_cibles_general.join(', ')}</p>
                        </div>
                        
                        <div>
                          <h4 className="font-medium text-white mb-1">Dosages :</h4>
                          {fertilizer.dosages.map((dosage, i) => (
                            <div key={i} className="bg-[#1c1a31] p-2 rounded mt-1">
                              <span className="font-medium text-[#d385f5]">{dosage.methode} :</span>
                              <span className="text-gray-300 ml-2">{dosage.dose}</span>
                            </div>
                          ))}
                        </div>
                        
                        <div>
                          <h4 className="font-medium text-white mb-1">Précautions :</h4>
                          <p className="text-orange-300 text-xs">{fertilizer.precautions_emploi}</p>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {DEFICIENCY_GUIDES.map((guide, index) => (
                <div
                  key={index}
                  className="bg-[#100f1c] p-4 rounded-lg border border-[#3D3B5E] cursor-pointer hover:border-[#d385f5] transition-colors"
                  onClick={() => setSelectedDeficiency(
                    selectedDeficiency?.element === guide.element ? null : guide
                  )}
                >
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-[#d385f5]">Carence en {guide.element}</h3>
                    {selectedDeficiency?.element === guide.element ? (
                      <ChevronUpIcon className="w-5 h-5 text-gray-400" />
                    ) : (
                      <ChevronDownIcon className="w-5 h-5 text-gray-400" />
                    )}
                  </div>
                  
                  {selectedDeficiency?.element === guide.element && (
                    <div className="mt-4 space-y-3 text-sm">
                      <div>
                        <h4 className="font-medium text-white mb-2">🔍 Symptômes :</h4>
                        <ul className="list-disc list-inside text-gray-300 space-y-1">
                          {guide.symptoms.map((symptom, i) => (
                            <li key={i}>{symptom}</li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="font-medium text-white mb-2">⚡ Causes :</h4>
                        <ul className="list-disc list-inside text-gray-300 space-y-1">
                          {guide.causes.map((cause, i) => (
                            <li key={i}>{cause}</li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="font-medium text-white mb-2">💊 Solutions :</h4>
                        <ul className="list-disc list-inside text-green-300 space-y-1">
                          {guide.solutions.map((solution, i) => (
                            <li key={i}>{solution}</li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="font-medium text-white mb-2">🛡️ Prévention :</h4>
                        <ul className="list-disc list-inside text-blue-300 space-y-1">
                          {guide.prevention.map((prevention, i) => (
                            <li key={i}>{prevention}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
