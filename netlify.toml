[build]
  # Commande de build pour Vite
  command = "npm run build"

  # Dossier de sortie après build
  publish = "dist"

[build.environment]
  # Version de Node.js pour le build
  NODE_VERSION = "18"

# Configuration des redirections pour SPA (Single Page Application)
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Configuration des headers de sécurité
[[headers]]
  for = "/*"
  [headers.values]
    # Sécurité HTTPS
    Strict-Transport-Security = "max-age=31536000; includeSubDomains"

    # Protection contre le clickjacking
    X-Frame-Options = "DENY"

    # Protection XSS
    X-Content-Type-Options = "nosniff"

    # Référer policy
    Referrer-Policy = "strict-origin-when-cross-origin"

# Configuration pour les assets statiques (cache)
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Configuration pour les fichiers JS/CSS
[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Configuration pour les images
[[headers]]
  for = "*.png"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.jpg"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.svg"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Configuration pour le manifest et favicon
[[headers]]
  for = "/manifest.json"
  [headers.values]
    Cache-Control = "public, max-age=86400"

[[headers]]
  for = "/favicon.ico"
  [headers.values]
    Cache-Control = "public, max-age=86400"