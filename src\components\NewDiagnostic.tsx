
import React, { useState, useCallback } from 'react';
import { Plant, GeminiDiagnosis } from '@/types';
import { useAuth } from '@/hooks/useAuth';
import { uploadImage, analyzePlantImages, addDiagnosticRecord } from '@/services/api';
import { Button } from '@/components/common/Button';
import { Spinner } from '@/components/common/Spinner';
import { CameraIcon, XMarkIcon } from '@/components/common/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { serverTimestamp, Timestamp } from 'firebase/firestore';
import imageCompression from 'browser-image-compression';

interface NewDiagnosticProps {
  plant: Plant;
  onFinish: () => void;
}

type Stage = 'upload' | 'analyzing' | 'result';

export const NewDiagnostic: React.FC<NewDiagnosticProps> = ({ plant, onFinish }) => {
  const { user } = useAuth();
  const [stage, setStage] = useState<Stage>('upload');
  const [files, setFiles] = useState<File[]>([]);
  const [previews, setPreviews] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<GeminiDiagnosis | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);

  const processFiles = async (fileList: FileList | File[]) => {
    const selectedFiles = Array.from(fileList);

    const options = {
        maxSizeMB: 1,
        maxWidthOrHeight: 1920,
        useWebWorker: true
    }

    try {
        const compressedFiles = await Promise.all(selectedFiles.map(file => imageCompression(file, options)));
        setFiles(prev => [...prev, ...compressedFiles]);

        const newPreviews = compressedFiles.map(file => URL.createObjectURL(file));
        setPreviews(prev => [...prev, ...newPreviews]);

    } catch (error) {
        console.error(error);
        setError("Échec de la compression des images. Veuillez réessayer.");
    }
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
        await processFiles(event.target.files);
    }
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const droppedFiles = Array.from(e.dataTransfer.files).filter(file =>
      file.type.startsWith('image/')
    );

    if (droppedFiles.length > 0) {
      await processFiles(droppedFiles);
    } else {
      setError("Veuillez déposer uniquement des fichiers image.");
    }
  }, []);

  const removeImage = (index: number) => {
    setFiles(files.filter((_, i) => i !== index));
    setPreviews(previews.filter((_, i) => i !== index));
  };
  
  const handleAnalyze = async () => {
    if (!user || files.length === 0) return;

    setIsLoading(true);
    setStage('analyzing');
    setError(null);

    try {
      const imageUrls = await Promise.all(
        files.map(file => uploadImage(file, `diagnostics/${user.uid}/${plant.id}/${Date.now()}_${file.name}`))
      );

      const base64Images = await Promise.all(
          files.map(file => imageCompression.getDataUrlFromFile(file).then(url => url.split(',')[1]))
      );

      const analysisResult = await analyzePlantImages(base64Images, plant.name);
      
      setResult(analysisResult);
      setStage('result');

    } catch (err) {
      console.error(err);
      setError('Une erreur s\'est produite pendant l\'analyse. Veuillez réessayer.');
      setStage('upload');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveResult = async () => {
      if(!user || !plant || !result) return;
      setIsLoading(true);
      try {
        const imageUrls = await Promise.all(
            files.map(file => uploadImage(file, `diagnostics/${user.uid}/${plant.id}/${Date.now()}_${file.name}`))
        );

        const newRecord = {
            userId: user.uid,
            plantId: plant.id,
            timestamp: serverTimestamp(),
            imageUrls,
            diagnosis: result,
        } as any;

        if(result.treatmentPlan.treatmentFrequencyDays > 0) {
            const nextDate = new Date();
            nextDate.setDate(nextDate.getDate() + result.treatmentPlan.treatmentFrequencyDays);
            newRecord.nextTreatmentDate = Timestamp.fromDate(nextDate);
        }

        await addDiagnosticRecord(user.uid, plant.id, newRecord);
        onFinish();

      } catch (error) {
          console.error("Failed to save result", error);
          setError("Impossible de sauvegarder l'enregistrement du diagnostic.");
      } finally {
          setIsLoading(false);
      }
  };

  const renderContent = () => {
    switch (stage) {
      case 'upload':
        return (
          <>
            <h2 className="text-3xl font-bold text-white mb-2">Nouveau Diagnostic pour {plant.name}</h2>
            <p className="text-[#E0E0E0] mb-6">Téléchargez des photos de votre plante. Pour de meilleurs résultats, incluez une image claire de la zone affectée.</p>
            <div
              className={`p-8 border-2 border-dashed rounded-2xl text-center transition-all duration-200 ${
                isDragOver
                  ? 'border-[#d385f5] bg-[#d385f5]/10'
                  : 'border-gray-600 bg-[#100f1c]'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <input type="file" id="file-upload" className="hidden" multiple accept="image/*" onChange={handleFileChange} />
              <label htmlFor="file-upload" className="cursor-pointer">
                <CameraIcon className={`w-12 h-12 mx-auto mb-4 transition-colors duration-200 ${
                  isDragOver ? 'text-[#d385f5]' : 'text-gray-400'
                }`} />
                <p className={`font-semibold transition-colors duration-200 ${
                  isDragOver ? 'text-[#d385f5]' : 'text-white'
                }`}>
                  {isDragOver ? 'Déposez vos images ici' : 'Cliquez pour télécharger des photos'}
                </p>
                <p className={`text-sm transition-colors duration-200 ${
                  isDragOver ? 'text-[#d385f5]/80' : 'text-gray-400'
                }`}>
                  ou glissez-déposez vos images
                </p>
              </label>
            </div>
            <AnimatePresence>
                {previews.length > 0 && (
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mt-6">
                    {previews.map((src, index) => (
                        <motion.div key={index} layout className="relative">
                            <img src={src} alt={`preview ${index}`} className="rounded-lg w-full h-32 object-cover"/>
                            <button onClick={() => removeImage(index)} className="absolute top-1 right-1 bg-black/50 rounded-full p-1 text-white">
                                <XMarkIcon className="w-4 h-4" />
                            </button>
                        </motion.div>
                    ))}
                    </div>
                )}
            </AnimatePresence>
            <div className="mt-8 flex justify-end gap-4">
              <Button variant="secondary" onClick={onFinish}>Annuler</Button>
              <Button onClick={handleAnalyze} disabled={files.length === 0} isLoading={isLoading}>Analyser les Images</Button>
            </div>
          </>
        );
      case 'analyzing':
        return (
            <div className="text-center">
                <Spinner size="lg" />
                <h2 className="text-3xl font-bold text-white mt-6">Analyse en cours...</h2>
                <p className="text-[#E0E0E0] mt-2">FloraSynth inspecte votre plante. Cela peut prendre un moment.</p>
            </div>
        );
      case 'result':
        return result && (
            <div>
                <h2 className="text-3xl font-bold text-white mb-2">Analyse Terminée</h2>
                <p className="text-xl text-white mb-4">Diagnostic : <span className="font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#d385f5] to-[#a364f7]">{result.disease}</span></p>
                <div className="space-y-6 text-[#E0E0E0] p-6 bg-[#100f1c] rounded-lg">
                    <p>{result.description}</p>

                    <div>
                        <h3 className="font-bold text-white text-lg mb-3">Plan de Traitement</h3>
                        <ul className="list-disc list-inside space-y-2 mb-4">
                            {result.treatmentPlan.steps.map((step, i) => <li key={i}>{step}</li>)}
                        </ul>
                        {result.treatmentPlan.treatmentFrequencyDays > 0 && (
                            <p className="font-semibold text-[#d385f5] mb-4">
                                Répéter le traitement tous les {result.treatmentPlan.treatmentFrequencyDays} jours.
                            </p>
                        )}
                    </div>

                    {result.treatmentPlan.recommendedProducts.length > 0 && (
                        <div>
                            <h3 className="font-bold text-white text-lg mb-3">Produits Recommandés et Dosages</h3>
                            <div className="space-y-4">
                                {result.treatmentPlan.recommendedProducts.map((product, i) => (
                                    <div key={i} className="bg-[#1c1a31] p-4 rounded-lg border border-[#3D3B5E]">
                                        <h4 className="font-semibold text-[#d385f5] mb-2">{product.name}</h4>
                                        <p className="text-sm text-gray-400 mb-3">{product.type}</p>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
                                            <div className="space-y-2">
                                                <h5 className="font-medium text-white text-sm">Dosages par contenant :</h5>
                                                <div className="text-sm space-y-1">
                                                    <div className="flex justify-between">
                                                        <span>Pulvérisateur 1L :</span>
                                                        <span className="font-medium text-[#d385f5]">{product.dosages.pulverisateur_1L}</span>
                                                    </div>
                                                    <div className="flex justify-between">
                                                        <span>Pulvérisateur 5L :</span>
                                                        <span className="font-medium text-[#d385f5]">{product.dosages.pulverisateur_5L}</span>
                                                    </div>
                                                    <div className="flex justify-between">
                                                        <span>Arrosoir 11L :</span>
                                                        <span className="font-medium text-[#d385f5]">{product.dosages.arrosoir_11L}</span>
                                                    </div>
                                                    <div className="flex justify-between">
                                                        <span>Arrosoir 13L :</span>
                                                        <span className="font-medium text-[#d385f5]">{product.dosages.arrosoir_13L}</span>
                                                    </div>
                                                    <div className="flex justify-between">
                                                        <span>Pulvérisateur 16L :</span>
                                                        <span className="font-medium text-[#d385f5]">{product.dosages.pulverisateur_16L}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="space-y-2">
                                                <h5 className="font-medium text-white text-sm">Application :</h5>
                                                <p className="text-sm">{product.applicationMethod}</p>
                                            </div>
                                        </div>

                                        {product.precautions && (
                                            <div className="mt-3 p-3 bg-orange-500/10 border border-orange-500/20 rounded">
                                                <h5 className="font-medium text-orange-400 text-sm mb-1">⚠️ Précautions :</h5>
                                                <p className="text-sm text-orange-300">{product.precautions}</p>
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    <div>
                        <h3 className="font-bold text-white text-lg mb-3">Conseils de Soin</h3>
                        <ul className="list-disc list-inside space-y-2">
                            {result.careTips.map((tip, i) => <li key={i}>{tip}</li>)}
                        </ul>
                    </div>
                </div>
                 <div className="mt-8 flex justify-end gap-4">
                    <Button variant="secondary" onClick={() => setStage('upload')}>Re-télécharger</Button>
                    <Button onClick={handleSaveResult} isLoading={isLoading}>Sauvegarder dans l'Historique</Button>
                </div>
            </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-[#100f1c]/80 backdrop-blur-sm">
      <motion.div 
        initial={{opacity: 0, scale: 0.95}}
        animate={{opacity: 1, scale: 1}}
        className="w-full max-w-3xl bg-[#1c1a31] p-8 rounded-2xl"
      >
        {error && <p className="text-red-400 mb-4 text-center">{error}</p>}
        {renderContent()}
      </motion.div>
    </div>
  );
};
